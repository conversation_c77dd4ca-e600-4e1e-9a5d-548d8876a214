/**
 * 用户管理服务
 * 处理用户管理相关的API调用
 */

import ApiClient from '@/lib/api-client';
import { ApiResponse } from '@/lib/api-utils';
import { User } from './auth.service';

// 用户相关的类型定义
export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
  role?: string;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  full_name?: string;
  role?: string;
  is_active?: boolean;
  phone?: string;
  gender?: 'male' | 'female' | 'other';
  birth_date?: string;
}

export interface UserListParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  vip_level?: string;
  is_active?: boolean;
  is_banned?: boolean;
  registration_source?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface UserListResponse {
  users: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UserStats {
  total: number;
  active: number;
  banned: number;
  vip: number;
  newThisMonth: number;
}

export interface BanUserRequest {
  reason: string;
  expiresAt?: string;
}

export interface SetVipRequest {
  level: 'none' | 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';
  expiresAt?: string;
}

// 用户管理服务类
export class UserService {
  /**
   * 获取用户列表
   */
  static async getUsers(params?: UserListParams): Promise<ApiResponse<UserListResponse>> {
    return ApiClient.get<UserListResponse>('/users', params);
  }

  /**
   * 根据ID获取用户详情
   */
  static async getUserById(id: number): Promise<ApiResponse<User>> {
    return ApiClient.get<User>(`/users/${id}`);
  }

  /**
   * 创建新用户
   */
  static async createUser(data: CreateUserRequest): Promise<ApiResponse<User>> {
    return ApiClient.post<User>('/users', data);
  }

  /**
   * 更新用户信息
   */
  static async updateUser(id: number, data: UpdateUserRequest): Promise<ApiResponse<User>> {
    return ApiClient.put<User>(`/users/${id}`, data);
  }

  /**
   * 删除用户
   */
  static async deleteUser(id: number): Promise<ApiResponse<void>> {
    return ApiClient.delete<void>(`/users/${id}`);
  }

  /**
   * 批量删除用户
   */
  static async deleteUsers(ids: number[]): Promise<ApiResponse<void>> {
    return ApiClient.post<void>('/users/batch-delete', { ids });
  }

  /**
   * 激活用户
   */
  static async activateUser(id: number): Promise<ApiResponse<User>> {
    return ApiClient.patch<User>(`/users/${id}/activate`);
  }

  /**
   * 停用用户
   */
  static async deactivateUser(id: number): Promise<ApiResponse<User>> {
    return ApiClient.patch<User>(`/users/${id}/deactivate`);
  }

  /**
   * 重置用户密码
   */
  static async resetUserPassword(id: number, newPassword: string): Promise<ApiResponse<void>> {
    return ApiClient.patch<void>(`/users/${id}/reset-password`, { password: newPassword });
  }

  /**
   * 更改用户角色
   */
  static async changeUserRole(id: number, role: string): Promise<ApiResponse<User>> {
    return ApiClient.patch<User>(`/users/${id}/role`, { role });
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats(): Promise<ApiResponse<UserStats>> {
    return ApiClient.get<UserStats>('/users/stats');
  }

  /**
   * 搜索用户
   */
  static async searchUsers(query: string, params?: Omit<UserListParams, 'search'>): Promise<ApiResponse<UserListResponse>> {
    return ApiClient.get<UserListResponse>('/users/search', { search: query, ...params });
  }

  /**
   * 导出用户数据
   */
  static async exportUsers(params?: UserListParams): Promise<ApiResponse<{ downloadUrl: string }>> {
    return ApiClient.get<{ downloadUrl: string }>('/users/export', params);
  }

  /**
   * 导入用户数据
   */
  static async importUsers(file: File): Promise<ApiResponse<{ imported: number; failed: number; errors?: string[] }>> {
    const formData = new FormData();
    formData.append('file', file);

    // 使用原生fetch处理文件上传
    const token = ApiClient.getToken();
    const response = await fetch('/api/users/import', {
      method: 'POST',
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
      },
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    return response.json();
  }

  /**
   * 获取用户活动日志
   */
  static async getUserActivityLog(
    id: number,
    params?: {
      page?: number;
      limit?: number;
      action?: string;
      date_from?: string;
      date_to?: string;
    }
  ): Promise<ApiResponse<{
    activities: Array<{
      id: number;
      action: string;
      resource: string;
      details: Record<string, unknown>;
      ip_address: string;
      user_agent: string;
      created_at: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }>> {
    return ApiClient.get(`/users/${id}/activity`, params);
  }

  /**
   * 获取用户会话信息
   */
  static async getUserSessions(id: number): Promise<ApiResponse<Array<{
    id: string;
    ip_address: string;
    user_agent: string;
    last_activity: string;
    is_current: boolean;
  }>>> {
    return ApiClient.get(`/users/${id}/sessions`);
  }

  /**
   * 终止用户会话
   */
  static async terminateUserSession(userId: number, sessionId: string): Promise<ApiResponse<void>> {
    return ApiClient.delete(`/users/${userId}/sessions/${sessionId}`);
  }

  /**
   * 终止用户所有会话
   */
  static async terminateAllUserSessions(userId: number): Promise<ApiResponse<void>> {
    return ApiClient.delete(`/users/${userId}/sessions`);
  }

  /**
   * 获取用户权限
   */
  static async getUserPermissions(id: number): Promise<ApiResponse<string[]>> {
    return ApiClient.get<string[]>(`/users/${id}/permissions`);
  }

  /**
   * 设置用户权限
   */
  static async setUserPermissions(id: number, permissions: string[]): Promise<ApiResponse<void>> {
    return ApiClient.put<void>(`/users/${id}/permissions`, { permissions });
  }

  /**
   * 发送用户通知
   */
  static async sendNotificationToUser(
    id: number,
    notification: {
      title: string;
      message: string;
      type?: 'info' | 'success' | 'warning' | 'error';
    }
  ): Promise<ApiResponse<void>> {
    return ApiClient.post<void>(`/users/${id}/notifications`, notification);
  }

  /**
   * 验证用户名是否可用
   */
  static async checkUsernameAvailability(username: string): Promise<ApiResponse<{ available: boolean }>> {
    return ApiClient.get<{ available: boolean }>('/users/check-username', { username });
  }

  /**
   * 封禁用户
   */
  static async banUser(id: number, banData: BanUserRequest): Promise<ApiResponse<void>> {
    return ApiClient.post<void>(`/users/${id}/ban`, banData);
  }

  /**
   * 解封用户
   */
  static async unbanUser(id: number): Promise<ApiResponse<void>> {
    return ApiClient.post<void>(`/users/${id}/unban`);
  }

  /**
   * 设置VIP等级
   */
  static async setVipLevel(id: number, vipData: SetVipRequest): Promise<ApiResponse<void>> {
    return ApiClient.post<void>(`/users/${id}/vip`, vipData);
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats(): Promise<ApiResponse<UserStats>> {
    return ApiClient.get<UserStats>('/users/stats');
  }

  /**
   * 验证邮箱是否可用
   */
  static async checkEmailAvailability(email: string): Promise<ApiResponse<{ available: boolean }>> {
    return ApiClient.get<{ available: boolean }>('/users/check-email', { email });
  }
}

export default UserService;
