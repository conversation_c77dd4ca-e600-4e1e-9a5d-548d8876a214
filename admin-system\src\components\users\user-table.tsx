"use client"

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, 
  UserCheck, 
  UserX, 
  Crown, 
  Eye, 
  Edit,
  Shield,
  ShieldOff
} from 'lucide-react';
import { User } from '@/services/auth.service';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface UserTableProps {
  users: User[];
  loading?: boolean;
  onBanUser: (user: User) => void;
  onUnbanUser: (user: User) => void;
  onSetVip: (user: User) => void;
  onViewUser: (user: User) => void;
  onEditUser: (user: User) => void;
}

export function UserTable({ 
  users, 
  loading, 
  onBanUser, 
  onUnbanUser, 
  onSetVip, 
  onViewUser, 
  onEditUser 
}: UserTableProps) {
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge variant="destructive">管理员</Badge>;
      case 'moderator':
        return <Badge variant="default">版主</Badge>;
      case 'user':
        return <Badge variant="secondary">用户</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  const getVipBadge = (level: string) => {
    const vipConfig = {
      none: { label: '普通', variant: 'outline' as const },
      bronze: { label: '青铜', variant: 'secondary' as const },
      silver: { label: '白银', variant: 'secondary' as const },
      gold: { label: 'default' as const, variant: 'default' as const },
      platinum: { label: '铂金', variant: 'default' as const },
      diamond: { label: '钻石', variant: 'destructive' as const },
    };

    const config = vipConfig[level as keyof typeof vipConfig] || vipConfig.none;
    
    return (
      <Badge variant={config.variant} className="gap-1">
        {level !== 'none' && <Crown className="h-3 w-3" />}
        {config.label}
      </Badge>
    );
  };

  const getStatusBadge = (user: User) => {
    if (user.is_banned) {
      return <Badge variant="destructive">已封禁</Badge>;
    }
    if (!user.is_active) {
      return <Badge variant="secondary">非活跃</Badge>;
    }
    return <Badge variant="default" className="bg-green-500">正常</Badge>;
  };

  const getRegistrationSourceBadge = (source: string) => {
    const sourceConfig = {
      web: { label: '网页', variant: 'outline' as const },
      mobile: { label: '移动端', variant: 'secondary' as const },
      api: { label: 'API', variant: 'default' as const },
      admin: { label: '管理员', variant: 'destructive' as const },
    };

    const config = sourceConfig[source as keyof typeof sourceConfig] || sourceConfig.web;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    try {
      return format(new Date(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN });
    } catch {
      return '-';
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
            <div className="h-10 w-10 bg-gray-200 rounded-full animate-pulse"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4"></div>
              <div className="h-3 bg-gray-200 rounded animate-pulse w-1/3"></div>
            </div>
            <div className="h-8 w-20 bg-gray-200 rounded animate-pulse"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>用户</TableHead>
            <TableHead>角色</TableHead>
            <TableHead>VIP等级</TableHead>
            <TableHead>状态</TableHead>
            <TableHead>注册来源</TableHead>
            <TableHead>登录次数</TableHead>
            <TableHead>最后登录</TableHead>
            <TableHead>注册时间</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.length === 0 ? (
            <TableRow>
              <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                暂无用户数据
              </TableCell>
            </TableRow>
          ) : (
            users.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.avatar_url || `https://avatar.vercel.sh/${user.username}`} />
                      <AvatarFallback>
                        {user.username.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{user.full_name || user.username}</div>
                      <div className="text-sm text-muted-foreground">
                        @{user.username} • {user.email}
                      </div>
                      {user.phone && (
                        <div className="text-xs text-muted-foreground">
                          {user.phone}
                        </div>
                      )}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{getRoleBadge(user.role)}</TableCell>
                <TableCell>{getVipBadge(user.vip_level)}</TableCell>
                <TableCell>{getStatusBadge(user)}</TableCell>
                <TableCell>{getRegistrationSourceBadge(user.registration_source)}</TableCell>
                <TableCell className="text-sm">{user.login_count}</TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {formatDate(user.last_login)}
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {formatDate(user.created_at)}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">打开菜单</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>操作</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => onViewUser(user)}>
                        <Eye className="mr-2 h-4 w-4" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onEditUser(user)}>
                        <Edit className="mr-2 h-4 w-4" />
                        编辑用户
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => onSetVip(user)}>
                        <Crown className="mr-2 h-4 w-4" />
                        设置VIP
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {user.is_banned ? (
                        <DropdownMenuItem onClick={() => onUnbanUser(user)}>
                          <ShieldOff className="mr-2 h-4 w-4" />
                          解除封禁
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem 
                          onClick={() => onBanUser(user)}
                          className="text-red-600"
                        >
                          <Shield className="mr-2 h-4 w-4" />
                          封禁用户
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
